import { Config } from 'config'
import { Kafka, Consumer } from 'kafkajs'
import logger from 'model/logger/logger'
import { ScrmCustomerEventToAiStaff, ScrmLinkReadMarkToAiStaff, ScrmMessageToAiStaff, ScrmOrderPlacedToAiStaff, ScrmReadMarkToAiStaff, ScrmWorkToAiStaff } from 'model/haogu/callback/type'
import { EventDispatcher } from './event_dispatch'
import { PrismaMongoClient } from 'haogu/src/database/prisma'
import fs from 'fs'

let consumer: Consumer | null = null

async function gracefulShutdown(signal?: string, exitCode = 0) {
  if (!consumer) {
    console.log(`Shutdown (${signal}): no consumer to disconnect`)
    process.exit(exitCode)
  }

  console.log(`Shutdown (${signal}): disconnecting consumer...`)
  try {
    // give disconnect up to 10s
    await Promise.race([
      consumer.disconnect(),
      new Promise((_, reject) => setTimeout(() => reject(new Error('disconnect timeout')), 10000))
    ])
    console.log('Consumer disconnected cleanly')
    process.exit(exitCode)
  } catch (err) {
    console.error('Error during consumer disconnect:', err)
    process.exit(1)
  }
}

async function main() {
  const enterpriseName = 'haogu'
  Config.setting.projectName = enterpriseName
  const config = {
    brokers: [
      'alikafka-serverless-cn-v3m4f2z0o04-1000.alikafka.aliyuncs.com:9093',
      'alikafka-serverless-cn-v3m4f2z0o04-2000.alikafka.aliyuncs.com:9093',
      'alikafka-serverless-cn-v3m4f2z0o04-3000.alikafka.aliyuncs.com:9093'
    ],
    sasl: {
      username: 'alikafka_serverless-cn-v3m4f2z0o04',
      password: 'mycB5EQyRKFB51jmGHWJnDnktpfy0nhw',
      mechanism: 'plain' as const
    },
    clientId: 'haogu-kafka-client',
    groupId: 'freespirit-haogu-product',
    ssl: {
      ca: [fs.readFileSync('./apps/haogu_dispatcher/only-4096-ca-cert', 'utf8')],
      rejectUnauthorized:false,
      // ca: [fs.readFileSync('./only-4096-ca-cert', 'utf8')],
    },
    connectionTimeout: 10000,
    requestTimeout: 30000,
    topics: {
      scrmWorkToAiStaff: 'scrm_ddm_work_to_ai_staff', //作业完成回调
      scrmMessageToAiStaff: 'scrm_message_to_ai_staff', //接收消息回调
      srcmReadMarkToAiStaff: 'scrm_readMark_to_ai_staff', //消息已读回调
      scrmCrmOrderPlacedToAiStaff: 'scrm_crm_order_placed_to_ai_staff', // 用户成单回调
      scrmLinkReadMarkToAIStaff:'scrm_link_readMark_to_ai_staff', //链接消息已读回调
      scrmCustomerEventToAiStaff:'scrm_customer_event_to_ai_staff', //好友通过删除回调
    }
  }

  const kafka = new Kafka({
    clientId: config.clientId,
    brokers: config.brokers,
    ssl: config.ssl,
    sasl: {
      mechanism: config.sasl.mechanism,
      username: config.sasl.username,
      password: config.sasl.password,
    },
    connectionTimeout: config.connectionTimeout || 10000,
    requestTimeout: config.requestTimeout || 30000,
  })

  consumer = kafka.consumer({ groupId:config.groupId })
  await consumer.connect()
  await consumer.subscribe({ topics:[config.topics.scrmCrmOrderPlacedToAiStaff, config.topics.scrmMessageToAiStaff, config.topics.scrmWorkToAiStaff, config.topics.srcmReadMarkToAiStaff, config.topics.scrmLinkReadMarkToAIStaff, config.topics.scrmCustomerEventToAiStaff] })

  // start message processing (minimal handler; adapt to your logic)
  await consumer.run({
    eachMessage: async ({ topic, message }) => {
      const value = message.value?.toString()
      logger.log(`receive ${topic}`, topic, value)
      if (!value) return
      if (topic == config.topics.scrmWorkToAiStaff) {
        const objectValue = JSON.parse(value) as ScrmWorkToAiStaff
        const custUnifiedUserId = objectValue.custUnifiedUserId
        const mongoClient = PrismaMongoClient.getInstance()
        const userInfo = await mongoClient.chat.findFirst({ where:{ contact:{ is:{ wx_id:custUnifiedUserId } } } })
        if (!userInfo?.wx_id) return
        EventDispatcher.dispatch(String(userInfo.wx_id), '/finish_work', { ...objectValue, chat_id:userInfo.id })
      } else if (topic == config.topics.scrmMessageToAiStaff) {
        const objectValue = JSON.parse(value) as ScrmMessageToAiStaff
        let customerToolUserId = objectValue.sender
        if (objectValue.isStaff) {
          customerToolUserId = objectValue.receiver
        }
        const mongoClient = PrismaMongoClient.getInstance()
        const userInfo = await mongoClient.chat.findFirst({ where:{ customer_tool_user_id:customerToolUserId } })
        if (!userInfo?.wx_id) return
        EventDispatcher.dispatch(String(userInfo.wx_id), '/message', { ...objectValue, chat_id:userInfo.id })
      } else if (topic == config.topics.srcmReadMarkToAiStaff) {
        const objectValue = JSON.parse(value) as ScrmReadMarkToAiStaff
        EventDispatcher.dispatch(String(objectValue.staffId), '/read_message', objectValue)
      } else if (topic == config.topics.scrmCrmOrderPlacedToAiStaff) {
        const objectValue = JSON.parse(value) as ScrmOrderPlacedToAiStaff
        const custUnifiedUserId = objectValue.unifiedUserId
        const mongoClient = PrismaMongoClient.getInstance()
        const userInfo = await mongoClient.chat.findFirst({ where:{ contact:{ is:{ wx_id:custUnifiedUserId } } } })
        if (!userInfo?.wx_id) return

        EventDispatcher.dispatch(String(userInfo.wx_id), '/order', { ...objectValue, chat_id:userInfo.id })
      } else if (topic == config.topics.scrmLinkReadMarkToAIStaff) {
        /* empty */
        const objectValue = JSON.parse(value) as ScrmLinkReadMarkToAiStaff
        EventDispatcher.dispatch(String(objectValue.staffId), '/read_link', objectValue)
      } else if (topic == config.topics.scrmCustomerEventToAiStaff) {
        const objectValue = JSON.parse(value) as ScrmCustomerEventToAiStaff
        EventDispatcher.dispatch(String(objectValue.staffId), '/new_customer', objectValue)
      } else {
        logger.error(`unknown topic ${topic}`)
      }
    }
  })

  // handle OS signals and unexpected errors
  const handle = (sig: string) => {
    console.log(`Received ${sig}, starting graceful shutdown`)
    gracefulShutdown(sig)
  }
  process.once('SIGINT', () => handle('SIGINT'))
  process.once('SIGTERM', () => handle('SIGTERM'))
  process.once('SIGQUIT', () => handle('SIGQUIT'))

  process.once('uncaughtException', (err) => {
    console.error('uncaughtException:', err)
    gracefulShutdown('uncaughtException', 1)
  })
  process.once('unhandledRejection', (reason) => {
    console.error('unhandledRejection:', reason)
    gracefulShutdown('unhandledRejection', 1)
  })
}

// start
main().catch((err) => {
  console.error('Startup error:', err)
  gracefulShutdown('startup error', 1)
})