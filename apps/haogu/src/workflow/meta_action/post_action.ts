import logger from 'model/logger/logger'
import { chatHistoryServiceClient, chatStateStoreClient } from '../../config/instance/base_instance'
import { IChattingFlag } from '../../config/manifest'
import { sleep } from 'openai/core'
import { getUserId } from 'config/chat_id'
import { DataService } from '../helper/get_data'
// import { SalesCase } from '../../helper/rag/sales_case'
import { ActionInfo } from 'service/agent/stage'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'
import { commonMessageSender } from '../../config/instance/send_message_instance'
import { commonSleep } from 'lib/schedule/schedule'
import { LLM } from 'lib/ai/llm/llm_model'
import { PromptTemplate, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../helper/register_task'
import { MaterialManager } from '../helper/material_manager'

export class PostAction {

  public static async sendInvitation() {
    const purchaseLink = 'https://www.integrity.com.cn/s/vEM'
    const actionInfo: ActionInfo = { guidance: `务必向客户发送下单链接：${purchaseLink}，并邀约客户购买` }
    return actionInfo
  }

  public static async reaskAnotherDay(chat_id: string) {
    logger.trace({ chat_id: chat_id }, '客户保留名额，次日询问')
    // await SilentReAsk.schedule(TaskName.reask_another_day, chat_id, 24 * 60 * 60 * 1000, {}, { auto_retry: true }) // 24小时后提醒客户
    return { guidance: '' } as ActionInfo
  }

  public static async sendGiftVideo(chat_id: string): Promise<ActionInfo> {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

    if (state.is_send_core_materials) {
      logger.log(`筹码峰核心资料已发送，跳过 Post Action for chat: ${chat_id}`)
      return {
        guidance: '之前给客户发过福利视频:\n' +
            '《双线合一核心解析》: a.ckjr001.com/xq4d3d/pe9joj3 (6分钟) \n' +
            '《四点共振实战应用》: a.ckjr001.com/0NkyNm/pe9joj3 (7分钟) \n' +
            '《主力锁仓和出货》: a.ckjr001.com/67a5RG/pe9joj3 \n' +
            '正常回复客户消息即可', callback: async () => {
        }
      }
    }
    return {
      guidance: '简单回复客户消息之后发送3节福利课链接:\n' +
          '《双线合一核心解析》: a.ckjr001.com/xq4d3d/pe9joj3 (6分钟) \n' +
          '《四点共振实战应用》: a.ckjr001.com/0NkyNm/pe9joj3 (7分钟) \n' +
          '《主力锁仓和出货》: a.ckjr001.com/67a5RG/pe9joj3 \n' +
          '并表示期待客户看完之后通知你', callback: async () => {
        // 更新状态
        await chatStateStoreClient.update(chat_id, {
          state: <IChattingFlag>{
            is_send_core_materials: true
          }
        })
      }
    }
  }

  public static async sendInstallationGuide(chat_id: string): Promise<ActionInfo> {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

    // 如果已经发送过，则不再发送
    if (state.is_send_installation_guide) {
      logger.log(`安装教程已发送，跳过 Post Action for chat: ${chat_id}`)
      return {
        guidance: '之前给客户发过安装教程:\n' +
            '手机版、电脑版app安装链接: https://download.9635.com.cn/\n' +
            '调出筹码峰视频教程: https://drive.weixin.qq.com/s?k=AGwAqAeQAA0d84u1Sv\n' +
            '正常回复客户消息即可', callback: async () => {
        }
      }
    }
    return {
      guidance: '指导客户安装好人好股app并且调出筹码峰:\n' +
          '手机版、电脑版app安装链接: https://download.9635.com.cn/\n' +
          '调出筹码峰视频教程: https://drive.weixin.qq.com/s?k=AGwAqAeQAA0d84u1Sv ', callback: async () => {
        await chatStateStoreClient.update(chat_id, {
          state: <IChattingFlag>{
            is_send_installation_guide: true,
            is_welcome_stage_completed: true
          }
        })
      }
    }
  }

  //   public static async askQuestionOne(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await commonMessageSender.sendText(chat_id, {
  //       text:'股市如江湖，有人快刀出鞘⚡，有人稳坐钓鱼台🎣。想知道自己现在的江湖段位吗？来个小测试，三组趣味题，1分钟帮你测出你的股市段位🤗',
  //       description:'开始进行挖需，介绍挖需测评小游戏'
  //     })
  //     await commonSleep()
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `先回答第一组问题，请直接回复字母，比如：A B C D
  // 1. 您的江湖身份：
  // A. 男青年（热血上头型）
  // B. 男中年（拼搏稳健型）
  // C. 老年退休（沉稳低调型）
  // D. 女股侠（清醒理智型）
  //
  // 2. 您征战股市多久：
  // A. 不到1年，刚入江湖
  // B. 1-3年，有点经验
  // C. 3-5年，老鸟但浮躁
  // D. 5年以上，资深沉淀
  //
  // 3. 您师从哪个门派？
  // A. 独门自创（纯凭感觉）
  // B. 江湖股吧（跟风信群）
  // C. 学术研读（书上高手）
  // D. 各种机构（培训常客）`,
  //       description: '挖需第一组问题，询问投资身份类型，进入股市时长，投机理念，三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
  //     }, {
  //       force:true
  //     })
  //     return { guidance: '挖需第一组题已发送，如果有没来得及回复的客户消息或客户问题简单回复一下', callback: async() => {} }
  //   }

  //   public static async askQuestionTwo(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `收到～ 请直接回字母回答接下来一组：
  //
  // 您惯用的炒股招式是：
  // A. 短线追涨杀跌（快进快出）
  // B. 中长线持股（耐心等待）
  // C. 消息面驱动（谁热买谁）
  // D. 随机操作（靠运气）
  //
  // 您的出手资金一般是多少？
  // A. 不到5万（新手尝试）
  // B. 5–30万（实操练级）
  // C. 30–100万（认真投资）
  // D. 100万以上（重仓对决）
  //
  // 您过去的炒股战绩是？
  // A. 稳定盈利（高手风范）
  // B. 时赚时亏（过山车）
  // C. 基本持平（佛系观望）
  // D. 经常亏损（割肉专业户）`,
  //       description: '挖需第二组问题，询问炒股技巧，炒股资金情况，过往炒股战绩,三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
  //     }, {
  //       force:true
  //     })
  //     return { guidance: '结束后续回复', callback: async() => {} }
  //   }
  //
  //   public static async askQuestionThree(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `收到～具体测评结果公布还差最后一步啦：
  //
  // 您最大的交易困扰是？
  // A. 不会选股，瞎买
  // B. 不会止损，爱扛单
  // C. 资金曲线乱，心情也乱
  // D. 没方法，全靠运气
  //
  // 您更偏好的收益方式？
  // A. 快进快出，追热点
  // B. 稳扎稳打，选结构
  // C. 中间摇摆，无法坚定
  // D. 想稳住但总忍不住
  //
  // 炒股对你来说是为了？
  // A. 一夜暴富
  // B. 赚点零花、补贴生活
  // C. 稳定理财、退休准备
  // D. 逆人性修炼自我纪律`,
  //       description: '挖需第三组问题，询问最大交易困扰，看什么判断买点，偏好的收益方式，炒股理由，三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
  //     }, {
  //       force:true
  //     })
  //     return { guidance: '结束后续回复', callback: async() => {} }
  //   }
  //   public static async askResultLevel(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await chatStateStoreClient.update(chat_id, {
  //       state:<IChattingFlag>{
  //         is_finish_stock_ranking_assessment:true
  //       }
  //     })
  //     const chatHistory = await chatHistoryServiceClient.getFormatChatHistoryByChatId(chat_id, true)
  //     const prompt = SystemMessagePromptTemplate.fromTemplate(`# 你的任务是根据客户的聊天信息判断出客户的具体炒股水平。
  // 请根据情况将用户分为5种等级
  // - level1:江湖菜鸟（新手试水，无体系，靠感觉）
  // - level2:小试牛刀（有经验但系统弱，风格不稳）
  // - level3:江湖老手（认知提升中，缺执行纪律）
  // - level4:理性剑客（思路清晰，结构意识强，缺落地）
  // - level5:宗师境界（系统成熟，目标清晰，等待放大）
  //
  // 以下是具体的聊天记录
  //
  // {chatHistory}
  //
  // # 输出要求
  // 请严格按照如下 JSON 格式输出
  // {{
  //   "think": "（深度思考）",
  //   "level": "（具体等级，直接输出数字等级）"
  // }}`)
  //     const output = await LLM.predict(
  //         prompt, {
  //           responseJSON: true,
  //           meta: {
  //             promptName: 'router',
  //             chat_id: chat_id,
  //             round_id: round_id,
  //           } }, {
  //           chatHistory
  //         })
  //     let level: number = 3
  //
  //     try {
  //       const parsedOutput = JSON.parse(output)
  //       level = parsedOutput.level
  //     } catch (error) {
  //       logger.error('Router 解析 JSON 失败:', error)
  //     }
  //     if (level == 1) {
  //       return await PostAction.askResultLevel1(chat_id, round_id)
  //     } else if (level == 2) {
  //       return await PostAction.askResultLevel2(chat_id, round_id)
  //     } else if (level == 3) {
  //       return await PostAction.askResultLevel3(chat_id, round_id)
  //     } else if (level == 4) {
  //       return await PostAction.askResultLevel4(chat_id, round_id)
  //     } else if (level == 5) {
  //       return await PostAction.askResultLevel5(chat_id, round_id)
  //     }
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `段位一｜江湖菜鸟
  // 您的段位是｜江湖菜鸟。您初入股市江湖，胆识过人，敢于下场，像少年侠客般一腔热血。勇气虽足，却多凭直觉出手，缺乏一套完整的心法，容易随风起舞，时而意外得手，时而仓促败退。气势上锐不可当，但心境尚浅，情绪易被市场牵动，出招凌厉却难以稳定。此时的您，如同初生牛犊，激情澎湃，却未能驭势控局。`,
  //       description: '股票段位评测结束了，你的评测结果是江湖菜鸟'
  //     })
  //     await sleep(5000)
  //     await commonMessageSender.sendText(chat_id, {
  //       text: '[笑脸]您看我测评的结果准吗？',
  //       description: '开放式问题'
  //     })
  //     return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  //   }
  //   public static async askResultLevel1(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `段位一｜江湖菜鸟
  // 您的段位是｜江湖菜鸟。您初入股市江湖，胆识过人，敢于下场，像少年侠客般一腔热血。勇气虽足，却多凭直觉出手，缺乏一套完整的心法，容易随风起舞，时而意外得手，时而仓促败退。气势上锐不可当，但心境尚浅，情绪易被市场牵动，出招凌厉却难以稳定。此时的您，如同初生牛犊，激情澎湃，却未能驭势控局。`,
  //       description: '股票段位评测结束了，你的评测结果是江湖菜鸟'
  //     })
  //     await sleep(5000)
  //     await commonMessageSender.sendText(chat_id, {
  //       text: '[笑脸]您看我测评的结果准吗？',
  //       description: '开放式问题'
  //     })
  //     return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  //   }
  //   public static async askResultLevel2(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `段位二｜小试牛刀
  // 您的段位是｜小试牛刀。您已在江湖中摸爬滚打一番，手中已有几招常用招式，偶尔能斩获战果。经验渐丰，但尚未凝练成稳定的套路，出手间常有摇摆，时而犀利，时而迟疑。气质上既有历练者的稳重，也带着江湖人心浮动的影子。整体来看，您已脱离莽撞，却仍在寻找一条真正适合自己的路，剑意初成，却未至圆融。`,
  //       description: '股票段位评测结束了，你的评测结果是小试牛刀'
  //     })
  //     await sleep(5000)
  //     await commonMessageSender.sendText(chat_id, {
  //       text: '[笑脸]您看我测评的结果准吗？',
  //       description: '开放式问题'
  //     })
  //     return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  //   }
  //   public static async askResultLevel3(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `段位三｜江湖老手
  // 您的段位是🐯｜江湖老手。您在股市江湖行走多年，眼界开阔，已能分辨虚实，心中逐渐生出框架。您能洞察行情起落，但在实战中，常因情势突变而动摇初心，计划难以始终如一。您的姿态像久经沙场的侠客，能握剑镇场，却难免被江湖风雨扰乱心神。整体而言，已能立足江湖，但剑意未稳，火候尚欠，仍需打磨心性与定力。`,
  //       description: '股票段位评测结束了，你的评测结果是江湖老手'
  //     })
  //     await sleep(5000)
  //     await commonMessageSender.sendText(chat_id, {
  //       text: '[笑脸]您看我测评的结果准吗？',
  //       description: '开放式问题'
  //     })
  //     return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  //   }
  //   public static async askResultLevel4(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `段位四｜理性剑客
  // 您的段位是｜理性剑客。您已能冷眼看市，结构与逻辑在心中逐渐成型，举止间透着从容。思维清晰，懂得分辨虚与实，能从乱象中找准落点。但在具体落地时，偶尔因迟疑错过良机，或在风云突变时未能果断出剑。您的气质如修炼多年的独行剑客，内功浑厚，步伐稳健，却在细节上仍有空隙。此刻的您，离圆满只差临门一脚。`,
  //       description: '股票段位评测结束了，你的评测结果是理性剑客'
  //     })
  //     await sleep(5000)
  //     await commonMessageSender.sendText(chat_id, {
  //       text: '[笑脸]您看我测评的结果准吗？',
  //       description: '开放式问题'
  //     })
  //     return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  //   }
  //   public static async askResultLevel5(chat_id:string, round_id:string):Promise<ActionInfo> {
  //     await commonMessageSender.sendText(chat_id, {
  //       text: `段位五｜宗师境界
  // 您的段位是｜宗师境界。您已登上股市江湖的高台，逻辑与体系融为一体，进退皆有章法，出剑从容不迫。无论行情起落，皆能保持节奏，展现大将之风。然而，宗师亦有桎梏，长年形成的套路虽稳固，却易固守成规，对新局面的适应未必敏捷。此时的您，如登峰造极的武林高手，已立于群山之巅，但若要再拓天地，还需不断求变。`,
  //       description: '股票段位评测结束了，你的评测结果是宗师境界'
  //     })
  //     await sleep(5000)
  //     await commonMessageSender.sendText(chat_id, {
  //       text: '[笑脸]您看我测评的结果准吗？',
  //       description: '开放式问题'
  //     })
  //     return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  //   }

  public static async sendEconomicCurve(chat_id: string, round_id: string): Promise<ActionInfo> {
    await commonMessageSender.sendMsg(chat_id, [{
      type: SendMessageType.image,
      url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/asset/expert_capital_curve.png',
      description: '高手资金曲线图片'
    }, {
      type: SendMessageType.text,
      text: '你看，这就是高手的资金曲线——平稳向上，没有剧烈的大起大落。交易的终局不是靠一次暴富改变命运，而是靠长期稳定复利，让资金曲线越来越漂亮。股市是“久富平台”，不是“暴富平台”。真正爽的交易，是每一次买卖都胸有成竹，利润是努力和学习的自然结果；而不是今天赚明天亏，情绪跟着账户上蹿下跳。你可以对照看下你的投资账户资金曲线，也可一发给我来看看🫣',
      description: '介绍高手资金曲线'
    }, {
      type: SendMessageType.text,
      text: '我们好人好股，是唯一一家把股民当成交易员进行专业赋能的培训机构。我们不推荐股票，而是授人以渔，真心希望教会学员一套稳定赚钱的交易方法，让每一次操作都有章可循。很开心在茫茫人海中遇到你！接下来，我们将开启一段6天的股民开悟之旅，让你彻底搞明白——你在股市赚的是什么钱、如何持续稳定地赚钱。📅 明天第一节课，记得准时来上课，我们直播见！',
      description: '介绍高手资金曲线后介绍好人好股'
    }])
    await chatStateStoreClient.update(chat_id, {
      state: <IChattingFlag>{
        after_bonding: true
      }
    })
    return {
      guidance: '如果感到需要就简单结尾一下，否则就不说话', callback: async () => {
      }
    }
  }

}