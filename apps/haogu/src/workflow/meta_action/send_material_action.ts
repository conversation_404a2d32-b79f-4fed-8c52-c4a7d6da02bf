import { PromptTemplate } from '@langchain/core/prompts'
import { LLM } from 'lib/ai/llm/llm_model'
import { MaterialManager } from '../helper/material_manager'
import { sleep } from 'openai/core'
import { commonMessageSender } from '../../config/instance/send_message_instance'
import { ActionInfo } from 'service/agent/stage'
import { DataService } from '../helper/get_data'


export class SendMaterialAction {


  public static async sendMaterial(chat_id:string, round_id:string, strategy: string | string[]) {
    const prompt = `# 角色设定
你是一个“素材调用助手”。

# 职责
- 你的工作不是执行对话策略，而是：**从对话策略中抽取出“需要发送/展示/提供”的素材目标**，并在素材目录中找到对应条目。  
- 忽略对话策略中所有与“发送/展示/提供”无关的步骤（如追问、协助、强调等）。

# 任务
- 仅在对话策略明确出现“发送/展示/提供/分享/推送/附上/引用/给出/调用”等动词时，才选择素材； 
- 最多选 3 条素材；若只有一个明确目标，只返回 1 条；
- 未命中上述动词指令，一律返回空数组 \`[]\`。

# 本轮对话策略
{strategy}

# 时间信息
{timeInfo}

# 素材目录结构
 - 公司介绍相关（可能包含的素材名称：公司好评案例）
 - 6天课程相关（可能包含的素材标题：福利课1:《双线合一核心解析》、福利课2:《四点共振实战应用》、福利课3:《主力锁仓和出货》 、第x节预习视频、第x节课后作业、第x节课后笔记）
 - 交易体系相关
 - 工具相关(可能包含的素材标题：手机/电脑版APP安装方法、多空趋势线指标工具、抄底先锋指标工具、主力进出指标工具)
 - 3360实战班相关
 - 成交相关
 
# 输出格式（仅输出 JSON，不要额外文字）
{
  “think”:"思考过程",
  "selected_materials": [
    {
      "category": "素材目录中的分类",
      "title": "素材标题"
    }
  ]
}`

    const promptTemplate = PromptTemplate.fromTemplate(prompt)

    const timeInfo = await SendMaterialAction.getTimInfo(chat_id)

    const res = await LLM.predict(promptTemplate, {
      meta: {
        round_id: round_id,
        chat_id: chat_id
      },
      responseJSON:true,
      reasoningEffort:'medium'
    }, { strategy, timeInfo })

    const jsonRes = JSON.parse(res)
    const selectedMaterials = jsonRes.selected_materials

    let caseInfo = '资料描述：'
    const sourceIdList: string[] = []

    for (const selectedMaterial of selectedMaterials) {
      const material = await new MaterialManager().searchMaterialByTitle(chat_id, selectedMaterial.title, selectedMaterial.category)
      if (material && await new MaterialManager().isValidCourseMaterial(chat_id, material?.title)) {
        caseInfo += `${material.description}`
        sourceIdList.push(material.source_id)
      }
    }

    const callBack = async () => {
      for (const sourceId of sourceIdList) {
        await sleep(3000)
        await commonMessageSender.sendMaterial(chat_id, { sourceId: sourceId })
      }
    }

    return {
      guidance: caseInfo,
      callback: callBack
    } as ActionInfo

  }

  private static async  getTimInfo(chatId: string): Promise<string> {
    const currentTime = await DataService.getCurrentTime(chatId)

    if (currentTime.day < 1) {
      return '课前'
    } else if (currentTime.day > 6) {
      return '课后'
    } else {
      const dayNames = ['一', '二', '三', '四', '五', '六']
      return `当前课程第${dayNames[currentTime.day - 1]}天`
    }
  }

}